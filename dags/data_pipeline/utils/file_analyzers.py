"""File analysis utilities for SVN data processing."""

import os
from typing import Union
from pathlib import Path

from ..custom_logger import setup_logger

logger = setup_logger(__name__)


class FileAnalyzer:
    """Analyzes file paths to determine their type and significance."""
    
    @staticmethod
    def is_app_index(filename: Union[str, Path]) -> bool:
        """
        Check if file is an application index file.
        
        Args:
            filename: File path to analyze
            
        Returns:
            True if file is an application index file
        """
        filename_str = str(filename)
        result = '/Core/' in filename_str and '4.Indexes' in filename_str
        
        if result:
            logger.debug(f"Identified app index file: {filename_str}")
        
        return result
    
    @staticmethod
    def is_report_index(filename: Union[str, Path]) -> bool:
        """
        Check if file is a reporting index file.
        
        Args:
            filename: File path to analyze
            
        Returns:
            True if file is a reporting index file
        """
        filename_str = str(filename)
        result = 'Reporting' in filename_str and '4.Indexes_Reporting' in filename_str
        
        if result:
            logger.debug(f"Identified report index file: {filename_str}")
        
        return result
    
    @staticmethod
    def is_primary_schema_change(filename: Union[str, Path]) -> bool:
        """
        Check if file represents a primary schema change.
        
        Args:
            filename: File path to analyze
            
        Returns:
            True if file is a primary schema change
        """
        filename_str = str(filename)
        file_extension = os.path.splitext(filename_str)[1]
        
        result = (
            file_extension == '.sql' 
            and '/Core/' in filename_str 
            and '1.Tables' in filename_str
        )
        
        if result:
            logger.debug(f"Identified primary schema change: {filename_str}")
        
        return result
    
    @staticmethod
    def is_primary_sql_change(filename: Union[str, Path]) -> bool:
        """
        Check if file represents a primary SQL change.
        
        Args:
            filename: File path to analyze
            
        Returns:
            True if file is a primary SQL change
        """
        filename_str = str(filename)
        file_base_name = os.path.basename(filename_str)
        file_extension = os.path.splitext(filename_str)[1]
        
        result = (
            file_extension == '.sql' 
            and file_base_name != 'Version.Sql'
            and '/Core/' in filename_str 
            and ('3.Updates' in filename_str or '2.USPs' in filename_str)
        )
        
        if result:
            logger.debug(f"Identified primary SQL change: {filename_str}")
        
        return result
    
    @staticmethod
    def is_conversion_script(filename: Union[str, Path]) -> bool:
        """
        Check if file is a conversion script.
        
        Args:
            filename: File path to analyze
            
        Returns:
            True if file is a conversion script
        """
        filename_str = str(filename)
        result = '/Core/' in filename_str and '5.Conversion' in filename_str
        
        if result:
            logger.debug(f"Identified conversion script: {filename_str}")
        
        return result
    
    @staticmethod
    def is_control_parameter_change(filename: Union[str, Path]) -> bool:
        """
        Check if file is a control parameter change.
        
        Args:
            filename: File path to analyze
            
        Returns:
            True if file is a control parameter change
        """
        filename_str = str(filename)
        result = '/Core/' in filename_str and '6.ControlScripts' in filename_str
        
        if result:
            logger.debug(f"Identified control parameter change: {filename_str}")
        
        return result
    
    @classmethod
    def analyze_file_changes(cls, filename: Union[str, Path]) -> dict[str, bool]:
        """
        Analyze a file and return all change types it represents.
        
        Args:
            filename: File path to analyze
            
        Returns:
            Dictionary with change type flags
        """
        return {
            'app_index_change': cls.is_app_index(filename),
            'report_index_change': cls.is_report_index(filename),
            'primary_schema_change': cls.is_primary_schema_change(filename),
            'primary_sql_change': cls.is_primary_sql_change(filename),
            'conversion_script': cls.is_conversion_script(filename),
            'control_parameters': cls.is_control_parameter_change(filename),
        }
    
    @classmethod
    def get_change_summary(cls, filename: Union[str, Path]) -> str:
        """
        Get a human-readable summary of changes for a file.
        
        Args:
            filename: File path to analyze
            
        Returns:
            String summary of change types
        """
        changes = cls.analyze_file_changes(filename)
        change_types = [key.replace('_', ' ').title() for key, value in changes.items() if value]
        
        if not change_types:
            return "No significant changes detected"
        
        return f"Changes detected: {', '.join(change_types)}"
