"""SVN client for interacting with Subversion repositories."""

import os
import subprocess
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from pathlib import Path

import xmltodict

from ..config import config
from ..custom_logger import setup_logger
from ..database.connection import KeePassCredentialManager

logger = setup_logger(__name__)


class SVNClientError(Exception):
    """Custom exception for SVN client errors."""
    pass


class SVNClient:
    """Client for interacting with SVN repositories."""
    
    def __init__(self):
        """Initialize SVN client with credentials from KeePass."""
        self.credential_manager = KeePassCredentialManager()
        self._username: Optional[str] = None
        self._password: Optional[str] = None
    
    @property
    def credentials(self) -> Tuple[str, str]:
        """Get SVN credentials (lazy loading)."""
        if self._username is None or self._password is None:
            self._username, self._password = self.credential_manager.get_svn_credentials()
        return self._username, self._password
    
    def _execute_command(self, command: str) -> <PERSON><PERSON>[str, str, int]:
        """
        Execute SVN command and return output.
        
        Args:
            command: SVN command to execute
            
        Returns:
            Tuple of (stdout, stderr, return_code)
            
        Raises:
            SVNClientError: If command execution fails
        """
        try:
            logger.debug(f"Executing SVN command: {command}")
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                text=True
            )
            
            stdout, stderr = process.communicate()
            return_code = process.returncode
            
            if return_code != 0:
                logger.error(f"SVN command failed with return code {return_code}: {stderr}")
            else:
                logger.debug(f"SVN command completed successfully")
            
            return stdout, stderr, return_code
            
        except Exception as e:
            logger.error(f"Failed to execute SVN command: {e}")
            raise SVNClientError(f"Command execution failed: {e}")
    
    def list_directory(
        self,
        svn_url: str,
        depth: str = "immediates",
        xml_output: bool = True
    ) -> dict:
        """
        List contents of SVN directory.
        
        Args:
            svn_url: SVN URL to list
            depth: Directory depth (immediates, infinity, empty, files)
            xml_output: Whether to return XML output
            
        Returns:
            Parsed XML dictionary or raw output
            
        Raises:
            SVNClientError: If listing fails
        """
        username, password = self.credentials
        
        command_parts = [
            'svn', 'ls', f'"{svn_url}"',
            f'--depth {depth}',
            f'--username {username}',
            f'--password {password}'
        ]
        
        if xml_output:
            command_parts.append('--xml')
        
        command = ' '.join(command_parts)
        stdout, stderr, return_code = self._execute_command(command)
        
        if return_code != 0:
            raise SVNClientError(f"Failed to list directory {svn_url}: {stderr}")
        
        if xml_output:
            try:
                parsed_xml = xmltodict.parse(stdout)
                logger.info(f"Successfully listed directory: {svn_url}")
                return parsed_xml
            except Exception as e:
                logger.error(f"Failed to parse XML output: {e}")
                raise SVNClientError(f"XML parsing failed: {e}")
        
        return {"output": stdout}
    
    def get_log(
        self,
        svn_url: str,
        verbose: bool = True,
        xml_output: bool = True,
        limit: Optional[int] = None
    ) -> dict:
        """
        Get SVN log for a URL.
        
        Args:
            svn_url: SVN URL to get log for
            verbose: Include changed paths
            xml_output: Return XML output
            limit: Limit number of log entries
            
        Returns:
            Parsed XML dictionary or raw output
            
        Raises:
            SVNClientError: If log retrieval fails
        """
        username, password = self.credentials
        
        command_parts = [
            'svn', 'log', f'"{svn_url}"',
            f'--username {username}',
            f'--password {password}'
        ]
        
        if verbose:
            command_parts.append('-v')
        
        if xml_output:
            command_parts.append('--xml')
        
        if limit:
            command_parts.append(f'--limit {limit}')
        
        command = ' '.join(command_parts)
        stdout, stderr, return_code = self._execute_command(command)
        
        if return_code != 0:
            raise SVNClientError(f"Failed to get log for {svn_url}: {stderr}")
        
        if xml_output:
            try:
                parsed_xml = xmltodict.parse(stdout)
                logger.info(f"Successfully retrieved log for: {svn_url}")
                return parsed_xml
            except Exception as e:
                logger.error(f"Failed to parse XML output: {e}")
                raise SVNClientError(f"XML parsing failed: {e}")
        
        return {"output": stdout}
    
    def check_server_availability(self, svn_server: str) -> bool:
        """
        Check if SVN server is available.
        
        Args:
            svn_server: SVN server URL
            
        Returns:
            True if server is available
        """
        try:
            # Try to list root directory
            result = self.list_directory(svn_server, depth="empty")
            return True
        except SVNClientError:
            logger.warning(f"SVN server {svn_server} is not available")
            return False
    
    def get_repository_info(self, svn_url: str) -> dict:
        """
        Get repository information.
        
        Args:
            svn_url: SVN URL to get info for
            
        Returns:
            Repository information dictionary
            
        Raises:
            SVNClientError: If info retrieval fails
        """
        username, password = self.credentials
        
        command = f'svn info "{svn_url}" --username {username} --password {password} --xml'
        stdout, stderr, return_code = self._execute_command(command)
        
        if return_code != 0:
            raise SVNClientError(f"Failed to get info for {svn_url}: {stderr}")
        
        try:
            parsed_xml = xmltodict.parse(stdout)
            logger.info(f"Successfully retrieved info for: {svn_url}")
            return parsed_xml
        except Exception as e:
            logger.error(f"Failed to parse XML output: {e}")
            raise SVNClientError(f"XML parsing failed: {e}")


# Global SVN client instance
svn_client = SVNClient()
