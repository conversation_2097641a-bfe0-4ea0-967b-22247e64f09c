"""Configuration management using python-dotenv."""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=env_path)


class Config:
    """Configuration class for the SVN data pipeline."""
    
    # Database Configuration
    PG_SERVER_NAME: str = os.getenv('PG_SERVER_NAME', 'localhost')
    PG_SERVER_PORT: str = os.getenv('PG_SERVER_PORT', '5432')
    PG_DB_NAME: str = os.getenv('PG_DB_NAME', 'jira')
    PG_SCHEMA: str = os.getenv('PG_SCHEMA', 'plat')
    
    # SVN Configuration
    SVN_SERVER_NAME: str = os.getenv('SVN_SERVER_NAME', 'svn://************')
    BRANCH_DIR: str = os.getenv('BRANCH_DIR', '/Applications/Lenox/Branch/')
    PACKAGE_DIR: str = os.getenv('PACKAGE_DIR', '/Applications/PlatProcessingReleasePackage')
    
    # KeePass Configuration
    KEEPASS_DB_PATH: str = os.getenv('KEEPASS_DB_PATH', 'E:/KeyPass/Database.kdbx')
    KEEPASS_KEY_PATH: str = os.getenv('KEEPASS_KEY_PATH', 'E:/KeyPass/Database.key')
    
    # KeePass Entry Names
    KEEPASS_SVN_ENTRY: str = os.getenv('KEEPASS_SVN_ENTRY', 'SVN')
    KEEPASS_PG_ENTRY_WINDOWS: str = os.getenv('KEEPASS_PG_ENTRY_WINDOWS', 'PG_DB')
    KEEPASS_PG_ENTRY_LINUX: str = os.getenv('KEEPASS_PG_ENTRY_LINUX', 'jiradb')
    KEEPASS_JIRA_ENTRY: str = os.getenv('KEEPASS_JIRA_ENTRY', 'corecard Jira')
    
    # Platform Version URL
    PLATFORM_VERSION_URL: str = os.getenv('PLATFORM_VERSION_URL', 'http://10.205.10.207')
    
    # HTTP Headers
    HTTP_ACCEPT: str = os.getenv('HTTP_ACCEPT', 'application/json')
    HTTP_CONTENT_TYPE: str = os.getenv('HTTP_CONTENT_TYPE', 'application/json')
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT: str = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Development Configuration
    ENVIRONMENT: str = os.getenv('ENVIRONMENT', 'development')
    DEBUG: bool = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # Connection Pool Settings
    DB_POOL_SIZE: int = int(os.getenv('DB_POOL_SIZE', '10'))
    DB_MAX_OVERFLOW: int = int(os.getenv('DB_MAX_OVERFLOW', '2'))
    DB_POOL_RECYCLE: int = int(os.getenv('DB_POOL_RECYCLE', '300'))
    DB_CONNECT_TIMEOUT: int = int(os.getenv('DB_CONNECT_TIMEOUT', '30'))
    DB_KEEPALIVES: int = int(os.getenv('DB_KEEPALIVES', '1'))
    DB_KEEPALIVES_IDLE: int = int(os.getenv('DB_KEEPALIVES_IDLE', '30'))
    DB_KEEPALIVES_INTERVAL: int = int(os.getenv('DB_KEEPALIVES_INTERVAL', '10'))
    DB_KEEPALIVES_COUNT: int = int(os.getenv('DB_KEEPALIVES_COUNT', '5'))
    
    # Data Processing Configuration
    DAYS_LOOKBACK: int = int(os.getenv('DAYS_LOOKBACK', '540'))
    EMAIL_DOMAIN: str = os.getenv('EMAIL_DOMAIN', '@corecard.com')
    
    # Temporary file paths
    TEMP_DIR: str = os.getenv('TEMP_DIR', '/tmp')
    
    @classmethod
    def get_keepass_db_path(cls) -> str:
        """Get KeePass database path based on OS."""
        if os.name == "nt":
            return cls.KEEPASS_DB_PATH
        elif os.uname().nodename == "airflow":
            return "/home/<USER>/Database.kdbx"
        else:
            return "/opt/airflow/dags/airflow/utility_code/Database.kdbx"
    
    @classmethod
    def get_keepass_key_path(cls) -> str:
        """Get KeePass key path based on OS."""
        if os.name == "nt":
            return cls.KEEPASS_KEY_PATH
        elif os.uname().nodename == "airflow":
            return "/home/<USER>/Database.key"
        else:
            return "/opt/airflow/dags/airflow/utility_code/Database.key"
    
    @classmethod
    def get_pg_entry_name(cls) -> str:
        """Get PostgreSQL entry name based on OS."""
        if os.name == "nt":
            return cls.KEEPASS_PG_ENTRY_WINDOWS
        elif os.uname().nodename == "airflow":
            return cls.KEEPASS_PG_ENTRY_LINUX
        else:
            return cls.KEEPASS_PG_ENTRY_WINDOWS


# Global config instance
config = Config()
