"""SQLAlchemy models for SVN data."""

from sqlalchemy import Column, Index, func
from sqlalchemy.dialects.postgresql import (
    TEXT, TIMESTAMP, INTEGER, BOOLEAN, ARRAY, SMALLINT
)

from .base import Base, TableName, CacheyCIText


class SVNBase:
    """Base class for SVN-related models."""
    
    __abstract__ = True
    
    name = Column(TEXT, primary_key=True, nullable=False)
    kind = Column(TEXT, nullable=False)
    revision = Column(INTEGER, nullable=False)
    author = Column(TEXT, nullable=False)
    author_email = Column(CacheyCIText, nullable=False, index=True)
    svn_server = Column(TEXT, nullable=False)


class SVNBranch(Base, TableName, SVNBase):
    """Model for SVN branch information."""
    
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    branch_directory = Column(TEXT, nullable=False, index=True)
    
    __table_args__ = (
        Index(
            "ix_svn_branch_created_on_date", 
            func.date(func.timezone('UTC', created_on))
        ),
    )


class SVNPackage(Base, TableName, SVNBase):
    """Model for SVN package information."""
    
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    package_directory = Column(TEXT, nullable=False, index=True)
    
    __table_args__ = (
        Index(
            "ix_svn_package_created_on_date", 
            func.date(func.timezone('UTC', created_on))
        ),
    )


class SVNCheckIn:
    """Base class for SVN check-in models."""
    
    __abstract__ = True
    
    revision = Column(INTEGER, nullable=False, primary_key=True)
    filename = Column(TEXT, nullable=False, primary_key=True)
    author = Column(TEXT, nullable=False)
    author_email = Column(CacheyCIText, nullable=False, index=True)
    comment = Column(TEXT)
    action = Column(TEXT)
    prop_mods = Column(TEXT, nullable=False)
    text_mods = Column(TEXT, nullable=False)
    kind = Column(TEXT, nullable=False)
    copyfrom_path = Column(TEXT)
    copyfrom_rev = Column(TEXT)
    cc_jira = Column(ARRAY(TEXT), nullable=True)
    client_jira = Column(ARRAY(TEXT), nullable=True)
    app_indexes = Column(BOOLEAN, nullable=False)
    report_indexes = Column(BOOLEAN, nullable=False)
    svn_server = Column(TEXT, nullable=False, primary_key=True)


class SVNBranchCheckIn(Base, TableName, SVNCheckIn):
    """Model for SVN branch check-in information."""
    
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    branch_name = Column(TEXT, nullable=False, primary_key=True)
    
    __table_args__ = (
        Index(
            "ix_svn_branch_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class SVNPackageCheckIn(Base, TableName, SVNCheckIn):
    """Model for SVN package check-in information."""
    
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    package_name = Column(TEXT, nullable=False, primary_key=True)
    
    __table_args__ = (
        Index(
            "ix_svn_package_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class CCJiraBaseCheckin:
    """Base class for CC JIRA check-in models."""
    
    __abstract__ = True
    
    revision = Column(INTEGER, nullable=False, primary_key=True)
    filename = Column(TEXT, nullable=False, primary_key=True)
    cc_jira = Column(TEXT, nullable=True, primary_key=True)
    author = Column(TEXT, nullable=False)
    author_email = Column(CacheyCIText, nullable=False, index=True)
    app_index_change = Column(SMALLINT, nullable=True)
    report_index_change = Column(SMALLINT, nullable=True)
    primary_schema_change = Column(SMALLINT, nullable=True)
    primary_sql_change = Column(SMALLINT, nullable=True)
    conversion_script = Column(SMALLINT, nullable=True)
    control_parameters = Column(SMALLINT, nullable=True)
    svn_server = Column(TEXT, nullable=False, primary_key=True)


class CCJiraBranchCheckIn(Base, TableName, CCJiraBaseCheckin):
    """Model for CC JIRA branch check-in information."""
    
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    branch_name = Column(TEXT, nullable=False, primary_key=True, index=True)
    
    __table_args__ = (
        Index(
            "ix_cc_jira_branch_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class CCJiraPackageCheckIn(Base, TableName, CCJiraBaseCheckin):
    """Model for CC JIRA package check-in information."""
    
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    package_name = Column(TEXT, nullable=False, primary_key=True, index=True)
    
    __table_args__ = (
        Index(
            "ix_cc_jira_package_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class PlatformVersion(Base, TableName):
    """Model for platform version information."""
    
    platform_version = Column(TEXT, primary_key=True, index=True, nullable=False)
    description = Column(ARRAY(TEXT))
    created_on = Column(TIMESTAMP(timezone=True), nullable=False)
