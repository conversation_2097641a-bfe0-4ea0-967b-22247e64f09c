"""Base database models and utilities."""

import re
from abc import ABC
from typing import Any, Dict, List

from sqlalchemy import <PERSON>aD<PERSON>
from sqlalchemy.ext.declarative import declared_attr, declarative_base
from sqlalchemy.orm import has_inherited_table, object_mapper
from citext import CIText


class CacheyCIText(CIText):
    """
    Workaround for https://github.com/mahmoudimus/sqlalchemy-citext/issues/25
    Can remove when that issue is fixed
    """
    cache_ok = True


class TableName:
    """Mixin to automatically generate table names from class names."""
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        if has_inherited_table(cls):
            return None
        
        # Convert CamelCase to snake_case
        split_camel_case = "_".join(
            re.sub('([A-Z][a-z]+)', r' \1', 
                   re.sub('([A-Z]+)', r' \1', cls.__name__)).split()
        )
        return split_camel_case.lower()


class RepresentableBase:
    """
    Base class that adds an automatic __repr__ method to all subclasses.
    
    The __repr__ will represent values as:
        ClassName(pkey_1=value_1, pkey_2=value_2, ..., pkey_n=value_n)
    where pkey_1..pkey_n are the primary key columns with their values.
    """
    
    __abstract__ = True
    
    def __repr__(self) -> str:
        """Generate string representation based on primary key columns."""
        mapper = object_mapper(self)
        items = [
            (p.key, getattr(self, p.key))
            for p in [mapper.get_property_by_column(c) for c in mapper.primary_key]
        ]
        return "{0}({1})".format(
            self.__class__.__name__,
            ', '.join(['{0}={1!r}'.format(*item) for item in items])
        )


# Database naming convention
convention = {
    "ix": 'ix_%(column_0_label)s',
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

# Create metadata with naming convention
metadata_obj = MetaData(naming_convention=convention, schema=None)

# Create declarative base
Base = declarative_base(cls=RepresentableBase, metadata=metadata_obj)


def transform_nested_list(alist: List[Dict[str, Any]]) -> Dict[str, List[Any]]:
    """
    Transform nested list structure from XML parsing.
    
    Args:
        alist: List of dictionaries from XML parsing
        
    Returns:
        Flattened dictionary with lists as values
    """
    output_dict: Dict[str, List[Any]] = {}
    
    for node in alist:
        for key, value in node.items():
            if isinstance(value, dict):
                for k2, v2 in value.items():
                    if isinstance(v2, list):
                        output_dict[k2] = output_dict.get(k2, []) + [v2]
                    else:
                        # converts dict into a list
                        output_dict[k2] = output_dict.get(k2, []) + [[v2]]
            else:
                output_dict[key] = output_dict.get(key, []) + [value]
    
    return output_dict
