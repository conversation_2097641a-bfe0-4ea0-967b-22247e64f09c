"""Database connection management with KeePass integration."""

import os
import sys
from contextlib import contextmanager
from typing import Generator, Optional
from urllib.parse import quote

import psycopg2
import sqlalchemy.exc
from pykeepass import PyKeePass
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session

from ..config import config
from ..custom_logger import setup_logger
from ..dbmodels.base import Base

logger = setup_logger(__name__)


class DatabaseConnectionError(Exception):
    """Custom exception for database connection errors."""
    pass


class KeePassCredentialManager:
    """Manages credentials retrieval from KeePass database."""
    
    def __init__(self):
        """Initialize KeePass credential manager."""
        self.keepass_db_path = config.get_keepass_db_path()
        self.keepass_key_path = config.get_keepass_key_path()
        self._keepass_db: Optional[PyKeePass] = None
    
    @property
    def keepass_db(self) -> PyKeePass:
        """Get KeePass database instance (lazy loading)."""
        if self._keepass_db is None:
            try:
                self._keepass_db = PyKeePass(
                    filename=self.keepass_db_path,
                    keyfile=self.keepass_key_path
                )
                logger.info(f"Successfully loaded KeePass database from {self.keepass_db_path}")
            except Exception as e:
                logger.error(f"Failed to load KeePass database: {e}")
                raise DatabaseConnectionError(f"KeePass database access failed: {e}")
        
        return self._keepass_db
    
    def get_database_credentials(self) -> tuple[str, str]:
        """
        Get database credentials from KeePass.
        
        Returns:
            Tuple of (username, password)
            
        Raises:
            DatabaseConnectionError: If credentials cannot be retrieved
        """
        try:
            entry_name = config.get_pg_entry_name()
            entry = self.keepass_db.find_entries(title=entry_name, first=True)
            
            if not entry:
                raise DatabaseConnectionError(f"Database entry '{entry_name}' not found in KeePass")
            
            logger.info(f"Retrieved database credentials for entry: {entry_name}")
            return entry.username, entry.password
            
        except Exception as e:
            logger.error(f"Failed to retrieve database credentials: {e}")
            raise DatabaseConnectionError(f"Database credential retrieval failed: {e}")
    
    def get_svn_credentials(self) -> tuple[str, str]:
        """
        Get SVN credentials from KeePass.
        
        Returns:
            Tuple of (username, password)
            
        Raises:
            DatabaseConnectionError: If credentials cannot be retrieved
        """
        try:
            entry = self.keepass_db.find_entries(title=config.KEEPASS_SVN_ENTRY, first=True)
            
            if not entry:
                raise DatabaseConnectionError(f"SVN entry '{config.KEEPASS_SVN_ENTRY}' not found in KeePass")
            
            logger.info(f"Retrieved SVN credentials for entry: {config.KEEPASS_SVN_ENTRY}")
            return entry.username, entry.password
            
        except Exception as e:
            logger.error(f"Failed to retrieve SVN credentials: {e}")
            raise DatabaseConnectionError(f"SVN credential retrieval failed: {e}")


class DatabaseManager:
    """Manages database connections and sessions."""
    
    def __init__(self, project_schema: str = "plat"):
        """
        Initialize database manager.
        
        Args:
            project_schema: Database schema name
        """
        self.project_schema = project_schema
        self.credential_manager = KeePassCredentialManager()
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
    
    def _build_connection_string(self) -> str:
        """
        Build PostgreSQL connection string with credentials from KeePass.
        
        Returns:
            Database connection string
            
        Raises:
            DatabaseConnectionError: If connection string cannot be built
        """
        try:
            username, password = self.credential_manager.get_database_credentials()
            
            # Handle different server configurations based on environment
            if os.name == "nt":
                server_name = config.PG_SERVER_NAME
                server_port = config.PG_SERVER_PORT
                server_db = config.PG_DB_NAME
            elif os.uname().nodename == 'airflow':
                server_name = '10.206.9.57'
                server_port = '5432'
                server_db = 'jira'
            else:
                server_name = config.PG_SERVER_NAME
                server_port = config.PG_SERVER_PORT
                server_db = config.PG_DB_NAME
            
            conn_str = (
                f'postgresql+psycopg2://{username}:{quote(password)}'
                f'@{server_name}:{server_port}/{server_db}'
            )
            
            logger.info(f"Built connection string for {server_name}:{server_port}/{server_db}")
            return conn_str
            
        except Exception as e:
            logger.error(f"Failed to build connection string: {e}")
            raise DatabaseConnectionError(f"Connection string building failed: {e}")
    
    @property
    def engine(self) -> Engine:
        """Get database engine (lazy loading)."""
        if self._engine is None:
            try:
                conn_str = self._build_connection_string()
                dbschema = f"{self.project_schema},public"
                
                self._engine = create_engine(
                    conn_str,
                    connect_args={
                        'options': f'-csearch_path={dbschema}',
                        'connect_timeout': config.DB_CONNECT_TIMEOUT,
                        "keepalives": config.DB_KEEPALIVES,
                        "keepalives_idle": config.DB_KEEPALIVES_IDLE,
                        "keepalives_interval": config.DB_KEEPALIVES_INTERVAL,
                        "keepalives_count": config.DB_KEEPALIVES_COUNT,
                    },
                    echo=config.DEBUG,
                    pool_pre_ping=True,
                    pool_use_lifo=True,
                    pool_recycle=config.DB_POOL_RECYCLE,
                    max_overflow=config.DB_MAX_OVERFLOW,
                    pool_size=config.DB_POOL_SIZE
                ).execution_options(schema_translate_map={None: self.project_schema})
                
                logger.info(f"Created database engine for schema: {self.project_schema}")
                
            except (sqlalchemy.exc.OperationalError, psycopg2.OperationalError) as e:
                logger.error(f"Database connection failed: {e}")
                raise DatabaseConnectionError(f"Database engine creation failed: {e}")
        
        return self._engine
    
    @property
    def session_factory(self) -> sessionmaker:
        """Get session factory (lazy loading)."""
        if self._session_factory is None:
            self._session_factory = sessionmaker(bind=self.engine)
            logger.info("Created session factory")
        
        return self._session_factory
    
    def create_tables(self) -> None:
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Successfully created all database tables")
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise DatabaseConnectionError(f"Table creation failed: {e}")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Get database session with automatic cleanup.
        
        Yields:
            SQLAlchemy session
            
        Raises:
            DatabaseConnectionError: If session creation fails
        """
        session = None
        try:
            session = self.session_factory()
            logger.debug("Created database session")
            yield session
            session.commit()
            logger.debug("Committed database session")
        except Exception as e:
            if session:
                session.rollback()
                logger.error(f"Rolled back database session due to error: {e}")
            raise
        finally:
            if session:
                session.close()
                logger.debug("Closed database session")


# Global database manager instance
db_manager = DatabaseManager()
