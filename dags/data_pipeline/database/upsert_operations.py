"""Database upsert operations for SVN data."""

from typing import Type, Tuple, Optional
import pandas as pd
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import Session

from ..custom_logger import setup_logger
from ..dbmodels.base import Base

logger = setup_logger(__name__)


class UpsertOperations:
    """Handles upsert operations for database models."""
    
    @staticmethod
    def upsert(
        session: Session,
        model: Type[Base],
        dataframe: pd.DataFrame,
        primary_key: str,
        no_update_cols: Tuple[str, ...] = (),
        on_conflict_update: bool = True
    ) -> None:
        """
        Perform upsert operation on database table.
        
        Args:
            session: SQLAlchemy session
            model: SQLAlchemy model class
            dataframe: Pandas DataFrame with data to upsert
            primary_key: Primary key column name for conflict resolution
            no_update_cols: Columns to exclude from updates
            on_conflict_update: Whether to update on conflict or ignore
            
        Raises:
            ValueError: If dataframe is empty or model is invalid
            Exception: If upsert operation fails
        """
        if dataframe.empty:
            logger.warning(f"Empty dataframe provided for {model.__name__}, skipping upsert")
            return
        
        try:
            table = model.__table__
            
            # Convert DataFrame to records
            records = dataframe.to_dict(orient='records')
            logger.info(f"Upserting {len(records)} records to {model.__name__}")
            
            # Create insert statement
            stmt = insert(table).values(records)
            
            if on_conflict_update:
                # Determine columns to update (exclude primary keys and no_update_cols)
                update_cols = [
                    c.name for c in table.c
                    if c not in list(table.primary_key.columns) 
                    and c.name not in no_update_cols
                ]
                
                # Create on conflict update statement
                on_conflict_stmt = stmt.on_conflict_do_update(
                    index_elements=table.primary_key.columns,
                    set_={k: getattr(stmt.excluded, k) for k in update_cols},
                    index_where=(
                        getattr(model, primary_key) == getattr(stmt.excluded, primary_key)
                    )
                )
                logger.debug(f"Upsert will update columns: {update_cols}")
            else:
                # Create on conflict do nothing statement
                on_conflict_stmt = stmt.on_conflict_do_nothing(
                    index_elements=table.primary_key.columns
                )
                logger.debug("Upsert will ignore conflicts")
            
            # Execute the statement
            result = session.execute(on_conflict_stmt)
            logger.info(f"Successfully upserted data to {model.__name__}")
            
        except Exception as e:
            logger.error(f"Failed to upsert data to {model.__name__}: {e}")
            raise
    
    @staticmethod
    def bulk_upsert(
        session: Session,
        operations: list[dict],
        batch_size: int = 1000
    ) -> None:
        """
        Perform multiple upsert operations in batches.
        
        Args:
            session: SQLAlchemy session
            operations: List of upsert operation dictionaries
            batch_size: Number of records per batch
            
        Each operation dict should contain:
            - model: SQLAlchemy model class
            - dataframe: Pandas DataFrame
            - primary_key: Primary key column name
            - no_update_cols: Columns to exclude from updates (optional)
            - on_conflict_update: Whether to update on conflict (optional)
        """
        try:
            for i, operation in enumerate(operations):
                logger.info(f"Processing bulk upsert operation {i+1}/{len(operations)}")
                
                model = operation['model']
                dataframe = operation['dataframe']
                primary_key = operation['primary_key']
                no_update_cols = operation.get('no_update_cols', ())
                on_conflict_update = operation.get('on_conflict_update', True)
                
                # Process in batches if dataframe is large
                if len(dataframe) > batch_size:
                    logger.info(f"Processing {len(dataframe)} records in batches of {batch_size}")
                    
                    for start_idx in range(0, len(dataframe), batch_size):
                        end_idx = min(start_idx + batch_size, len(dataframe))
                        batch_df = dataframe.iloc[start_idx:end_idx]
                        
                        logger.debug(f"Processing batch {start_idx}-{end_idx}")
                        UpsertOperations.upsert(
                            session=session,
                            model=model,
                            dataframe=batch_df,
                            primary_key=primary_key,
                            no_update_cols=no_update_cols,
                            on_conflict_update=on_conflict_update
                        )
                else:
                    UpsertOperations.upsert(
                        session=session,
                        model=model,
                        dataframe=dataframe,
                        primary_key=primary_key,
                        no_update_cols=no_update_cols,
                        on_conflict_update=on_conflict_update
                    )
            
            logger.info(f"Successfully completed {len(operations)} bulk upsert operations")
            
        except Exception as e:
            logger.error(f"Failed to complete bulk upsert operations: {e}")
            raise
