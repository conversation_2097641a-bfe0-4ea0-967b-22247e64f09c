"""Custom logging configuration for the SVN data pipeline."""

import logging
import sys
from pathlib import Path
from typing import Optional
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.console import Console

from .config import config


def setup_logger(
    name: str,
    level: Optional[str] = None,
    log_file: Optional[Path] = None,
    correlation_id: Optional[str] = None
) -> logging.Logger:
    """
    Set up a structured logger with rich formatting and optional file output.
    
    Args:
        name: Logger name (usually __name__)
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional file path for log output
        correlation_id: Optional correlation ID for tracking operations
        
    Returns:
        Configured logger instance
        
    Raises:
        ValueError: If invalid log level is provided
    """
    logger = logging.getLogger(name)
    
    # Set log level
    log_level = level or config.LOG_LEVEL
    try:
        logger.setLevel(getattr(logging, log_level.upper()))
    except AttributeError:
        raise ValueError(f"Invalid log level: {log_level}")
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Console handler with rich formatting
    console = Console(stderr=True)
    console_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True
    )
    
    # Create formatter
    formatter = logging.Formatter(
        fmt=config.LOG_FORMAT,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Add correlation ID to logger if provided
    if correlation_id:
        logger = logging.LoggerAdapter(logger, {'correlation_id': correlation_id})
    
    return logger


class CorrelationLogger:
    """Logger with correlation ID support for tracking operations."""
    
    def __init__(self, name: str, correlation_id: str):
        """
        Initialize correlation logger.
        
        Args:
            name: Logger name
            correlation_id: Unique identifier for tracking operations
        """
        self.correlation_id = correlation_id
        self.logger = setup_logger(name, correlation_id=correlation_id)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message with correlation ID."""
        self.logger.info(f"[{self.correlation_id}] {message}", **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message with correlation ID."""
        self.logger.error(f"[{self.correlation_id}] {message}", **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message with correlation ID."""
        self.logger.warning(f"[{self.correlation_id}] {message}", **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message with correlation ID."""
        self.logger.debug(f"[{self.correlation_id}] {message}", **kwargs)


# Default logger for the module
logger = setup_logger(__name__)
