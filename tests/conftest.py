"""Pytest configuration and fixtures."""

import os
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from dags.data_pipeline.dbmodels.base import Base
from dags.data_pipeline.config import config


@pytest.fixture(scope="session")
def test_database():
    """Create a test database for integration tests."""
    # Use in-memory SQLite for testing
    engine = create_engine("sqlite:///:memory:", echo=False)
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(bind=engine)
    
    yield SessionLocal
    
    # Cleanup
    Base.metadata.drop_all(engine)


@pytest.fixture
def db_session(test_database):
    """Create a database session for testing."""
    session = test_database()
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture
def sample_svn_xml():
    """Sample SVN XML response for testing."""
    return """<?xml version="1.0" encoding="UTF-8"?>
    <lists>
        <list path="svn://example.com/repo">
            <entry kind="dir">
                <name>branch1</name>
                <commit revision="123">
                    <author>testuser</author>
                    <date>2023-01-01T12:00:00.000000Z</date>
                </commit>
            </entry>
            <entry kind="dir">
                <name>branch2</name>
                <commit revision="124">
                    <author>testuser2</author>
                    <date>2023-01-02T12:00:00.000000Z</date>
                </commit>
            </entry>
        </list>
    </lists>"""


@pytest.fixture
def sample_svn_log_xml():
    """Sample SVN log XML response for testing."""
    return """<?xml version="1.0" encoding="UTF-8"?>
    <log>
        <logentry revision="123">
            <author>testuser</author>
            <date>2023-01-01T12:00:00.000000Z</date>
            <msg>Test commit message PLAT-123</msg>
            <paths>
                <path action="M" kind="file">/Core/1.Tables/test.sql</path>
                <path action="A" kind="file">/Core/4.Indexes/index.sql</path>
            </paths>
        </logentry>
    </log>"""


@pytest.fixture
def sample_dataframe():
    """Sample DataFrame for testing."""
    return pd.DataFrame({
        'name': ['test1', 'test2'],
        'kind': ['dir', 'dir'],
        'revision': [123, 124],
        'author': ['user1', 'user2'],
        'author_email': ['<EMAIL>', '<EMAIL>'],
        'svn_server': ['svn://test.com', 'svn://test.com'],
        'created_on': ['2023-01-01 12:00:00', '2023-01-02 12:00:00']
    })


@pytest.fixture
def mock_keepass():
    """Mock KeePass database for testing."""
    mock_entry = Mock()
    mock_entry.username = "testuser"
    mock_entry.password = "testpass"
    
    mock_keepass = Mock()
    mock_keepass.find_entries.return_value = mock_entry
    
    with patch('dags.data_pipeline.database.connection.PyKeePass') as mock_class:
        mock_class.return_value = mock_keepass
        yield mock_keepass


@pytest.fixture
def mock_svn_command():
    """Mock SVN command execution for testing."""
    with patch('subprocess.Popen') as mock_popen:
        mock_process = Mock()
        mock_process.communicate.return_value = ("test output", "")
        mock_process.returncode = 0
        mock_popen.return_value = mock_process
        yield mock_popen


@pytest.fixture
def temp_env_file():
    """Create a temporary .env file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
        f.write("""
PG_SERVER_NAME=localhost
PG_SERVER_PORT=5432
PG_DB_NAME=test_db
SVN_SERVER_NAME=svn://test.com
KEEPASS_DB_PATH=/tmp/test.kdbx
KEEPASS_KEY_PATH=/tmp/test.key
        """)
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    os.unlink(temp_path)


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    original_env = os.environ.copy()
    
    # Set test environment variables
    test_env = {
        'ENVIRONMENT': 'test',
        'DEBUG': 'True',
        'LOG_LEVEL': 'DEBUG',
        'PG_SERVER_NAME': 'localhost',
        'PG_SERVER_PORT': '5432',
        'PG_DB_NAME': 'test_db',
        'SVN_SERVER_NAME': 'svn://test.com',
        'KEEPASS_DB_PATH': '/tmp/test.kdbx',
        'KEEPASS_KEY_PATH': '/tmp/test.key',
    }
    
    os.environ.update(test_env)
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


# Pytest markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: fast isolated tests")
    config.addinivalue_line("markers", "integration: component interaction tests")
    config.addinivalue_line("markers", "functional: end-to-end functionality tests")
    config.addinivalue_line("markers", "performance: performance and load tests")
    config.addinivalue_line("markers", "smoke: basic smoke tests")
    config.addinivalue_line("markers", "regression: regression tests")
    config.addinivalue_line("markers", "async: asynchronous tests")
    config.addinivalue_line("markers", "slow: tests that take more than 1 second")
    config.addinivalue_line("markers", "database: tests requiring database connection")
    config.addinivalue_line("markers", "svn: tests requiring SVN server connection")
