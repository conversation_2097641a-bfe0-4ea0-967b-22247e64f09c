"""Unit tests for file analyzers."""

import pytest
import allure
from dags.data_pipeline.utils.file_analyzers import FileAnalyzer


@allure.epic("Data Pipeline")
@allure.feature("File Analysis")
class TestFileAnalyzer:
    """Test file analysis functionality."""
    
    @allure.story("App Index Detection")
    @allure.title("Should detect application index files")
    @pytest.mark.unit
    def test_is_app_index_positive(self):
        """Test detection of application index files."""
        # Given
        test_files = [
            "/Core/Database/4.Indexes/app_index.sql",
            "/Applications/Core/4.Indexes/test.sql",
            "/Core/4.Indexes/index.sql"
        ]
        
        # When & Then
        for filename in test_files:
            with allure.step(f"Check file: {filename}"):
                assert FileAnalyzer.is_app_index(filename) is True
    
    @allure.story("App Index Detection")
    @allure.title("Should not detect non-app index files")
    @pytest.mark.unit
    def test_is_app_index_negative(self):
        """Test rejection of non-application index files."""
        # Given
        test_files = [
            "/Reporting/4.Indexes_Reporting/report.sql",
            "/Core/1.Tables/table.sql",
            "/Other/4.Indexes/other.sql",
            "4.Indexes/standalone.sql"
        ]
        
        # When & Then
        for filename in test_files:
            with allure.step(f"Check file: {filename}"):
                assert FileAnalyzer.is_app_index(filename) is False
    
    @allure.story("Report Index Detection")
    @allure.title("Should detect reporting index files")
    @pytest.mark.unit
    def test_is_report_index_positive(self):
        """Test detection of reporting index files."""
        # Given
        test_files = [
            "/Reporting/Database/4.Indexes_Reporting/report.sql",
            "/Applications/Reporting/4.Indexes_Reporting/test.sql"
        ]
        
        # When & Then
        for filename in test_files:
            with allure.step(f"Check file: {filename}"):
                assert FileAnalyzer.is_report_index(filename) is True
    
    @allure.story("Schema Change Detection")
    @allure.title("Should detect primary schema changes")
    @pytest.mark.unit
    def test_is_primary_schema_change(self):
        """Test detection of primary schema changes."""
        # Given
        positive_files = [
            "/Core/Database/1.Tables/users.sql",
            "/Applications/Core/1.Tables/orders.sql"
        ]
        
        negative_files = [
            "/Core/1.Tables/users.txt",  # Wrong extension
            "/Reporting/1.Tables/report.sql",  # Not in Core
            "/Core/2.USPs/procedure.sql"  # Wrong directory
        ]
        
        # When & Then
        for filename in positive_files:
            with allure.step(f"Check positive file: {filename}"):
                assert FileAnalyzer.is_primary_schema_change(filename) is True
        
        for filename in negative_files:
            with allure.step(f"Check negative file: {filename}"):
                assert FileAnalyzer.is_primary_schema_change(filename) is False
    
    @allure.story("SQL Change Detection")
    @allure.title("Should detect primary SQL changes")
    @pytest.mark.unit
    def test_is_primary_sql_change(self):
        """Test detection of primary SQL changes."""
        # Given
        positive_files = [
            "/Core/Database/2.USPs/procedure.sql",
            "/Core/3.Updates/update.sql"
        ]
        
        negative_files = [
            "/Core/2.USPs/Version.Sql",  # Excluded file
            "/Core/2.USPs/procedure.txt",  # Wrong extension
            "/Reporting/2.USPs/procedure.sql"  # Not in Core
        ]
        
        # When & Then
        for filename in positive_files:
            with allure.step(f"Check positive file: {filename}"):
                assert FileAnalyzer.is_primary_sql_change(filename) is True
        
        for filename in negative_files:
            with allure.step(f"Check negative file: {filename}"):
                assert FileAnalyzer.is_primary_sql_change(filename) is False
    
    @allure.story("Conversion Script Detection")
    @allure.title("Should detect conversion scripts")
    @pytest.mark.unit
    def test_is_conversion_script(self):
        """Test detection of conversion scripts."""
        # Given
        positive_files = [
            "/Core/5.Conversion/convert.sql",
            "/Applications/Core/5.Conversion/migration.py"
        ]
        
        negative_files = [
            "/Reporting/5.Conversion/convert.sql",
            "/Core/1.Tables/table.sql"
        ]
        
        # When & Then
        for filename in positive_files:
            with allure.step(f"Check positive file: {filename}"):
                assert FileAnalyzer.is_conversion_script(filename) is True
        
        for filename in negative_files:
            with allure.step(f"Check negative file: {filename}"):
                assert FileAnalyzer.is_conversion_script(filename) is False
    
    @allure.story("Control Parameter Detection")
    @allure.title("Should detect control parameter changes")
    @pytest.mark.unit
    def test_is_control_parameter_change(self):
        """Test detection of control parameter changes."""
        # Given
        positive_files = [
            "/Core/6.ControlScripts/config.sql",
            "/Applications/Core/6.ControlScripts/params.py"
        ]
        
        negative_files = [
            "/Reporting/6.ControlScripts/config.sql",
            "/Core/1.Tables/table.sql"
        ]
        
        # When & Then
        for filename in positive_files:
            with allure.step(f"Check positive file: {filename}"):
                assert FileAnalyzer.is_control_parameter_change(filename) is True
        
        for filename in negative_files:
            with allure.step(f"Check negative file: {filename}"):
                assert FileAnalyzer.is_control_parameter_change(filename) is False
    
    @allure.story("Comprehensive Analysis")
    @allure.title("Should analyze all change types for a file")
    @pytest.mark.unit
    def test_analyze_file_changes(self):
        """Test comprehensive file analysis."""
        # Given
        filename = "/Core/Database/4.Indexes/app_index.sql"
        
        # When
        with allure.step("Analyze file changes"):
            changes = FileAnalyzer.analyze_file_changes(filename)
        
        # Then
        with allure.step("Verify analysis results"):
            assert changes['app_index_change'] is True
            assert changes['report_index_change'] is False
            assert changes['primary_schema_change'] is False
            assert changes['primary_sql_change'] is False
            assert changes['conversion_script'] is False
            assert changes['control_parameters'] is False
    
    @allure.story("Change Summary")
    @allure.title("Should generate human-readable change summary")
    @pytest.mark.unit
    def test_get_change_summary(self):
        """Test change summary generation."""
        # Given
        test_cases = [
            ("/Core/4.Indexes/app.sql", "Changes detected: App Index Change"),
            ("/Other/file.txt", "No significant changes detected"),
            ("/Core/1.Tables/table.sql", "Changes detected: Primary Schema Change")
        ]
        
        # When & Then
        for filename, expected_summary in test_cases:
            with allure.step(f"Check summary for: {filename}"):
                summary = FileAnalyzer.get_change_summary(filename)
                assert expected_summary in summary
