[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "airflow-svn"
version = "0.1.0"
description = "Apache Airflow DAG for SVN data processing and PostgreSQL integration"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.10"

# Production dependencies
dependencies = [
    # Core Airflow (compatible with 3.0)
    "apache-airflow>=2.8.0,<3.1.0",
    "apache-airflow-providers-postgres>=5.0.0",
    
    # Database - SQLAlchemy 1.4.x for Airflow 3.0 compatibility
    "sqlalchemy>=1.4.50,<2.0.0",
    "psycopg2-binary>=2.9.0",
    "asyncpg>=0.29.0",
    
    # Data processing
    "pandas>=2.0.0",
    
    # HTTP and XML processing
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
    "xmltodict>=0.13.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    
    # Date/time handling
    "python-dateutil>=2.8.0",
    "pytz>=2023.3",
    
    # Security and credentials
    "pykeepass>=4.0.0",
    "python-dotenv>=1.0.0",
    
    # Text processing
    "sqlalchemy-citext>=1.8.0",
    
    # Dependency injection and utilities
    "dependency-injector>=4.41.0",
    "rich>=13.0.0",
    "pathlib2>=2.3.7; python_version<'3.11'",
]

[project.optional-dependencies]
# Development dependencies
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "pytest-xdist>=3.3.0",
    "pytest-bdd>=6.1.0",
    "allure-pytest>=2.13.0",
    
    # Code quality
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    
    # Type stubs
    "types-requests>=2.31.0",
    "types-python-dateutil>=2.8.0",
    "types-pytz>=2023.3.0",
    
    # Documentation
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
    
    # Deployment
    "fabric>=3.2.0",
]

# Performance and monitoring
performance = [
    "psutil>=5.9.0",
    "memory-profiler>=0.61.0",
]

# All optional dependencies
all = [
    "airflow-svn[dev,performance]"
]

[project.urls]
Homepage = "https://github.com/yourusername/airflow-svn"
Repository = "https://github.com/yourusername/airflow-svn"
Issues = "https://github.com/yourusername/airflow-svn/issues"

[project.scripts]
# Add any CLI scripts here if needed

# Tool configurations
[tool.setuptools.packages.find]
where = ["."]
include = ["dags*", "tests*"]

[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = [
    "--strict-markers",
    "--alluredir=allure_results", 
    "--clean-alluredir",
    "--asyncio-mode=auto",
    "--cov=dags",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=80"
]
markers = [
    "unit: fast isolated tests",
    "integration: component interaction tests", 
    "functional: end-to-end functionality tests",
    "performance: performance and load tests",
    "smoke: basic smoke tests",
    "regression: regression tests",
    "async: Asynchronous tests",
    "slow: tests that take more than 1 second",
    "database: tests requiring database connection",
    "svn: tests requiring SVN server connection"
]
asyncio_mode = "auto"
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.black]
line-length = 100
target-version = ["py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 100
known_first_party = ["dags", "tests"]
known_third_party = ["airflow", "sqlalchemy", "pandas", "requests"]
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 100
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "build",
    "dist",
    "*.egg-info",
    "migrations"
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
    "tests/*:S101",      # use of assert
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "xmltodict.*",
    "pykeepass.*",
    "citext.*",
    "fabric.*",
    "allure.*"
]
ignore_missing_imports = true

[tool.coverage.run]
source = ["dags"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"
