# Airflow SVN Data Pipeline

A Python 3.10+ Apache Airflow application for processing SVN repository data and populating PostgreSQL databases using SQLAlchemy 1.4.x.

## Features

- **SVN Integration**: Automated extraction of branch and package information from SVN repositories
- **Database Operations**: Efficient upsert operations using PostgreSQL and SQLAlchemy 1.4.x
- **Security**: Credential management through KeePass integration
- **Configuration**: Environment-based configuration using python-dotenv
- **Type Safety**: Full type hints and mypy compatibility
- **Testing**: Comprehensive test suite with pytest and allure reporting
- **Code Quality**: Black, isort, flake8, and mypy integration

## Project Structure

```
airflow-svn/
├── dags/
│   ├── data_pipeline/
│   │   ├── config.py              # Configuration management
│   │   ├── custom_logger.py       # Structured logging
│   │   ├── database/
│   │   │   ├── connection.py      # Database connection management
│   │   │   └── upsert_operations.py # Database upsert operations
│   │   ├── dbmodels/
│   │   │   ├── base.py            # Base models and utilities
│   │   │   └── svn_models.py      # SVN-specific models
│   │   ├── svn/
│   │   │   └── client.py          # SVN client implementation
│   │   └── utils/
│   │       └── file_analyzers.py # File analysis utilities
│   └── svn_dag.py                 # Main Airflow DAG
├── tests/                         # Test suite
├── .env                          # Environment variables
├── .env.example                  # Environment template
├── pyproject.toml                # Project configuration
└── README.md                     # This file
```

## Requirements

- Python 3.10+
- PostgreSQL database
- SVN server access
- KeePass database for credential management
- Apache Airflow 2.8+ (compatible with 3.0)

## Installation

### 1. Set up Virtual Environment

```bash
# Create virtual environment using uv
uv venv
.\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Install dependencies
uv pip install -e .
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Update paths, server addresses, and other settings
```

### 3. Set up KeePass

Ensure your KeePass database contains entries for:
- `SVN`: SVN server credentials
- `PG_DB` (Windows) / `jiradb` (Linux): PostgreSQL credentials
- `corecard Jira`: JIRA credentials (if needed)

### 4. Database Setup

The application will automatically create tables on first run. Ensure your PostgreSQL server is accessible and the database exists.

## Usage

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage and allure reporting
pytest --alluredir=allure_results --clean-alluredir --cov=dags --cov-report=html

# Run specific test categories
pytest -m "unit"
pytest -m "integration"
pytest -m "not slow"

# Generate allure report
allure serve allure_results
```

### Code Quality

```bash
# Format and lint code
black . && isort . && flake8 && mypy dags/
```

### Running the DAG

The main DAG (`svn-dag-new`) includes the following tasks:

1. **add_branch_details**: Extract SVN branch information
2. **add_package_details**: Extract SVN package information  
3. **update_checkin_details**: Process branch check-in data
4. **add_package_checkin**: Process package check-in data
5. **update_platform_version**: Update platform version information

### Development Mode

For development and testing:

```python
# Run individual functions
if __name__ == '__main__':
    from dags.data_pipeline.database.connection import db_manager
    
    # Test database connection
    with db_manager.get_session() as session:
        print("Database connection successful!")
```

## Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

- `PG_SERVER_NAME`: PostgreSQL server hostname
- `PG_SERVER_PORT`: PostgreSQL server port
- `PG_DB_NAME`: Database name
- `SVN_SERVER_NAME`: SVN server URL
- `KEEPASS_DB_PATH`: Path to KeePass database
- `KEEPASS_KEY_PATH`: Path to KeePass key file

### Database Configuration

The application uses SQLAlchemy 1.4.x for compatibility with Apache Airflow 3.0:

- Connection pooling with configurable parameters
- Schema translation for multi-tenant support
- Automatic table creation
- Efficient upsert operations using PostgreSQL's `ON CONFLICT`

### Logging

Structured logging with:
- Rich console output with syntax highlighting
- Configurable log levels
- Correlation IDs for operation tracking
- File output support

## Architecture

### Database Models

- **SVNBranch/SVNPackage**: Repository structure information
- **SVNBranchCheckIn/SVNPackageCheckIn**: Check-in details with file analysis
- **CCJiraBranchCheckIn/CCJiraPackageCheckIn**: JIRA-linked changes
- **PlatformVersion**: Platform release information

### File Analysis

Automatic detection of:
- Application index changes
- Reporting index changes  
- Primary schema changes
- SQL script changes
- Conversion scripts
- Control parameter changes

### Security

- KeePass integration for credential management
- No hardcoded credentials in source code
- Environment-based configuration
- Secure database connections with SSL support

## Testing

Comprehensive test suite with:

- **Unit tests**: Individual component testing
- **Integration tests**: Database and SVN integration
- **Performance tests**: Load and stress testing
- **Allure reporting**: Rich test reports with screenshots and logs

Test markers:
- `unit`: Fast isolated tests
- `integration`: Component interaction tests
- `async`: Asynchronous tests
- `slow`: Tests taking >1 second
- `database`: Tests requiring database connection
- `svn`: Tests requiring SVN server connection

## Contributing

1. Follow the existing code style (Black, isort)
2. Add type hints to all functions
3. Write comprehensive tests
4. Update documentation
5. Ensure all quality checks pass

## License

MIT License - see LICENSE file for details.
