import getpass
from fabric import task
from fabric import Connection, Config


@task
def welcome(ctx):
    print("Welcome to getting started with Fabric!")


@task
def deploy(ctx):
    # sudo_pass = getpass.getpass("Enter sudo password:")
    # config = Config(overrides={'sudo': {'password': sudo_pass}})

    # c = Connection(host='***********', user='airflow', port=22, config=config)
    c = Connection(host='***********', user='airflow', port=22)
    result = c.run('uname -s', pty=True)
    print(result)

    c.run('cd /opt/airflow-svn/ && git pull')
    c.run('cp /opt/airflow-svn/svn_dag.py /home/<USER>/airflow/dags')
    # c.sudo('systemctl restart index')
    # c.sudo('systemctl restart celery')
