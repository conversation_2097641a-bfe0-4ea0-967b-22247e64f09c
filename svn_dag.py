import os
import sys
from datetime import datetime, timedelta

import pytz
from dateutil import tz, parser as date_util_parser

if os.name != 'nt':
    try:
        from airflow import DAG
        from airflow.operators.python import PythonOperator

        from airflow.triggers.base import BaseTrigger, TriggerEvent
        from airflow.providers.postgres.operators.postgres import PostgresOperator
    except ImportError as e:
        raise  e

import requests
import xmltodict
from subprocess import Popen, PIPE
import pandas as pd
import json
from pykeepass import PyKeePass as pkp
from urllib.parse import quote, urlparse
import sqlalchemy.exc

import psycopg2
import psycopg2.extensions as psy_extension

from sqlalchemy import Column, create_engine, select, Index, func
from sqlalchemy.dialects.postgresql import insert, dialect, TEXT, TIMESTAMP, INTEGER, BOOLEAN, ARRAY, SMALLINT
from sqlalchemy.orm import sessionmaker, aliased, object_mapper, declarative_base, has_inherited_table
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql.schema import MetaD<PERSON>

from bs4 import BeautifulSoup
import re
from html.parser import HTMLParser
from abc import ABC
from citext import CIText


class CacheyCIText(CIText):
    # Workaround for https://github.com/mahmoudimus/sqlalchemy-citext/issues/25
    # Can remove when that issue is fixed
    cache_ok = True


class Parser(HTMLParser, ABC):

    def __init__(self, start_tags, end_tags, all_data, comments):
        super().__init__()
        self.start_tags = start_tags
        self.end_tags = end_tags
        self.all_data = all_data
        self.comments = comments

    # method to append the start tag to the list start_tags.
    def handle_starttag(self, tag, attrs):
        self.start_tags.append(tag)

    # method to append the end tag to the list end_tags.
    def handle_endtag(self, tag):
        self.end_tags.append(tag)

    # method to append the data between the tags to the list all_data.
    def handle_data(self, data):
        self.all_data.append(data)

    # method to append the comment to the list comments.
    def handle_comment(self, data):
        self.comments.append(data)


class TableName(object):
    @declared_attr
    def __tablename__(cls):
        if has_inherited_table(cls):
            return None
        split_camel_case = "_".join(re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', cls.__name__)).split())
        # print(f"Table Name in base.py {split_camel_case.lower()}")
        return split_camel_case.lower()


class RepresentableBase(object):
    """
    This class can be used by ``declarative_base``, to add an automatic
    ``__repr__`` method to *all* subclasses of ``Base``. This ``__repr__`` will
    represent values as::
        ClassName(pkey_1=value_1, pkey_2=value_2, ..., pkey_n=value_n)
    where ``pkey_1..pkey_n`` are the primary key columns of the mapped table
    with the corresponding values.
    """

    # Since the tables defined here are only 'skeleton' tables used as
    # the base for our schema-specific tables, we set abstract to True.
    __abstract__ = True

    def __repr__(self):
        mapper = object_mapper(self)
        items = [(p.key, getattr(self, p.key))
                 for p in [
                     mapper.get_property_by_column(c) for c in mapper.primary_key]]
        return "{0}({1})".format(
            self.__class__.__name__,
            ', '.join(['{0}={1!r}'.format(*_) for _ in items]))


convention = {
    "ix": 'ix_%(column_0_label)s',
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata_obj = MetaData(naming_convention=convention, schema=None)
Base = declarative_base(cls=RepresentableBase, metadata=metadata_obj)


class SVNBase:
    __abstract__ = True
    name = Column(TEXT, primary_key=True, nullable=False)
    kind = Column(TEXT, nullable=False)
    revision = Column(INTEGER, nullable=False)
    author = Column(TEXT, nullable=False)
    author_email = Column(CacheyCIText, nullable=False, index=True)
    svn_server = Column(TEXT, nullable=False)


class SVNBranch(Base, TableName, SVNBase):
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    branch_directory = Column(TEXT, nullable=False, index=True)
    __table_args__ = (
        Index("ix_svn_branch_created_on_date", func.date(func.timezone('UTC', created_on))),
    )


class SVNPackage(Base, TableName, SVNBase):
    created_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    package_directory = Column(TEXT, nullable=False, index=True)
    __table_args__ = (
        Index("ix_svn_package_created_on_date", func.date(func.timezone('UTC', created_on))),
    )


class SVNCheckIn:
    __abstract__ = True
    revision = Column(INTEGER, nullable=False, primary_key=True)
    filename = Column(TEXT, nullable=False, primary_key=True)
    author = Column(TEXT, nullable=False)
    author_email = Column(CacheyCIText, nullable=False, index=True)
    comment = Column(TEXT)
    action = Column(TEXT)
    prop_mods = Column(TEXT, nullable=False)
    text_mods = Column(TEXT, nullable=False)
    kind = Column(TEXT, nullable=False)
    copyfrom_path = Column(TEXT)
    copyfrom_rev = Column(TEXT)
    cc_jira = Column(ARRAY(TEXT), nullable=True)
    client_jira = Column(ARRAY(TEXT), nullable=True)
    app_indexes = Column(BOOLEAN, nullable=False)
    report_indexes = Column(BOOLEAN, nullable=False)
    svn_server = Column(TEXT, nullable=False, primary_key=True)


class SVNBranchCheckIn(Base, TableName, SVNCheckIn):
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    branch_name = Column(TEXT, nullable=False, primary_key=True)
    __table_args__ = (
        Index(
            "ix_svn_branch_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class SVNPackageCheckIn(Base, TableName, SVNCheckIn):
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    package_name = Column(TEXT, nullable=False, primary_key=True)
    __table_args__ = (
        Index(
            "ix_svn_package_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),

    )


class CCJiraBaseCheckin:
    __abstract__ = True
    revision = Column(INTEGER, nullable=False, primary_key=True)
    filename = Column(TEXT, nullable=False, primary_key=True)
    cc_jira = Column(TEXT, nullable=True, primary_key=True)
    author = Column(TEXT, nullable=False)
    author_email = Column(CacheyCIText, nullable=False, index=True)
    app_index_change = Column(SMALLINT, nullable=True)
    report_index_change = Column(SMALLINT, nullable=True)
    primary_schema_change = Column(SMALLINT, nullable=True)
    primary_sql_change = Column(SMALLINT, nullable=True)
    conversion_script = Column(SMALLINT, nullable=True)
    control_parameters = Column(SMALLINT, nullable=True)
    svn_server = Column(TEXT, nullable=False, primary_key=True)


class CCJiraBranchCheckIn(Base, TableName, CCJiraBaseCheckin):
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    branch_name = Column(TEXT, nullable=False, primary_key=True, index=True)
    __table_args__ = (
        Index(
            "ix_cc_jira_branch_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class CCJiraPackageCheckIn(Base, TableName, CCJiraBaseCheckin):
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    package_name = Column(TEXT, nullable=False, primary_key=True, index=True)
    __table_args__ = (
        Index(
            "ix_cc_jira_package_check_in_checked_in_on_date",
            func.date(func.timezone('UTC', checked_in_on))
        ),
    )


class PlatformVersion(Base, TableName):
    platform_version = Column(TEXT, primary_key=True, index=True, nullable=False)
    description = Column(ARRAY(TEXT))
    created_on = Column(TIMESTAMP(timezone=True), nullable=False)


def transform_nested_list(alist: list) -> dict:
    output_dict = {}
    for node in alist:
        for key, value in node.items():
            if isinstance(value, dict):
                for k2, v2, in value.items():
                    if isinstance(v2, list):
                        output_dict[k2] = output_dict.get(k2, []) + [v2]
                    else:
                        # converts dict into a list
                        output_dict[k2] = output_dict.get(k2, []) + [[v2]]
            else:
                output_dict[key] = output_dict.get(key, []) + [value]
    return output_dict


def handle_pyscopg2_exception(err):
    # get details about the exception
    err_type, err_obj, traceback = sys.exc_info()

    # get the line number when exception occurred
    line_num = traceback.tb_lineno
    print('exiting with Failure !!!')


def start_session(prjkey: str = "plat"):
    dbschema = f"{prjkey},public"
    conn_str = None
    if os.name == "nt":
        filename = r"E:\KeyPass\config.json"
    elif os.uname().nodename == "airflow":
        filename = "/home/<USER>/airflow/dags/utility_code/config.json"
    else:
        filename = "/opt/airflow/dags/airflow/utility_code/config.json"

    keedb = f'KeePassDB_{os.name}'
    keepass = f'KeyPassKey_{os.name}'

    with open(filename, "r") as fp:
        data = json.load(fp)
    ref = pkp(filename=data[keedb], keyfile=data[keepass])

    if os.name == "nt":
        entry = ref.find_entries(title='PG_DB', first=True)
        user = entry.username
        pwd = entry.password

        server_name = os.getenv('PG_SERVER_NAME', 'localhost')
        server_port = os.getenv('PG_SERVER_PORT', '5432')
        server_db = os.getenv('PG_DB_NAME', 'jira')

        conn_str = f'postgresql+psycopg2://' + user + ':' + quote(
            pwd) + '@' + server_name + ':' + server_port + '/' + server_db

    elif os.uname().nodename == 'airflow':
        entry = ref.find_entries(title='jiradb', first=True)
        user = entry.username
        pwd = entry.password

        conn_str = 'postgresql+psycopg2://{username}:{password}@***********:5432/{database}'.format(
            username=user,
            password=quote(pwd),
            database='jira'
        )
    else:
        entry = ref.find_entries(title='PG_DB', first=True)
        user = entry.username
        pwd = entry.password

        server_name = os.getenv('PG_SERVER_NAME', None)
        server_port = os.getenv('PG_SERVER_PORT', None)
        server_db = os.getenv('PG_DB_NAME', None)

        conn_str = f'postgresql+psycopg2://' + user + ':' + quote(
            pwd) + '@' + server_name + ':' + server_port + '/' + server_db

    try:
        engine_ = create_engine(
            conn_str,
            connect_args={
                'options': '-csearch_path={}'.format(dbschema), 'connect_timeout': 30,
                "keepalives": 1,
                "keepalives_idle": 30,
                "keepalives_interval": 10,
                "keepalives_count": 5,
            },
            echo=False,
            pool_pre_ping=True,
            pool_use_lifo=True,
            pool_recycle=300, max_overflow=2, pool_size=10
        ).execution_options(schema_translate_map={None: prjkey})

        session = sessionmaker()
        session.configure(bind=engine_)

        # Base.metadata.create_all(
        #     engine_.execution_options(schema_translate_map={None: prjkey}),
        # )
        Base.metadata.create_all(bind=engine_)

        return session()
    except sqlalchemy.exc.OperationalError as e:
        handle_pyscopg2_exception(e)
        exit(1)
    except psycopg2.OperationalError as e:
        handle_pyscopg2_exception(e)
        exit(1)


def upsert(session, model, rows: pd.DataFrame, primary_key: str, no_update_cols: tuple = (), on_conflict_update=True):
    # Source: https://stackoverflow.com/questions/7165998/how-to-do-an-upsert-with-sqlalchemy/44395983#44395983
    table = model.__table__
    stmt = insert(table).values(rows.to_dict(orient='records'))

    update_cols = [
        c.name for c in table.c
        if c not in list(table.primary_key.columns) and c.name not in no_update_cols
    ]

    if on_conflict_update:
        on_conflict_stmt = stmt.on_conflict_do_update(
            index_elements=table.primary_key.columns,
            set_={k: getattr(stmt.excluded, k) for k in update_cols},
            index_where=(getattr(model, primary_key) == getattr(stmt.excluded, primary_key))
        )
    else:
        on_conflict_stmt = stmt.on_conflict_do_nothing(index_elements=table.primary_key.columns)

    session.execute(on_conflict_stmt)


def populate_svn_package_details(**kwargs):
    svn_server = kwargs['params']['svn_server']
    package_directories = kwargs['params']['package_directory']
    project = kwargs['params']['project']

    home_path = os.getenv('HOME', 'E:/KeyPass')
    ref = pkp(
        filename=os.path.join(home_path, 'Database.kdbx'),
        keyfile=os.path.join(home_path, 'Database.key')
    )

    entry = ref.find_entries(title='SVN', first=True)
    user = entry.username
    pwd = entry.password

    for package_dir in package_directories:
        command = f'svn ls {svn_server}{package_dir} --depth immediates --xml --username {user} --password {pwd}'
        p = Popen(command, stdout=PIPE, stderr=PIPE, shell=True)
        stdout, stderr = p.communicate()
        if p.returncode == 0:
            svn_package_dict = xmltodict.parse(stdout.decode())['lists']['list']['entry']
            # Drop files added in base directory
            svn_package_dict = [item for item in svn_package_dict if item['@kind'] != 'file']

            # for index, node in enumerate(svn_package_dict):
            #     if node['@kind'] == 'file':
            #         # Drop files added in base directory
            #         svn_package_dict.pop(index)
            svn_package_dict = transform_nested_list(svn_package_dict)

            df = pd.DataFrame.from_dict(svn_package_dict).set_index('name').apply(pd.Series.explode).reset_index()
            df.columns = df.columns.str.replace('@', '')
            # Coulmn names: name kind revision             author                         date
            df.rename(columns={'date': 'created_on'}, inplace=True)
            df['svn_server'] = svn_server
            df['package_directory'] = package_dir
            df['author_email'] = df['author'] + '@corecard.com'
            session = start_session(project)
            upsert(session, SVNPackage, df, 'name', on_conflict_update=True)
            session.commit()
        else:
            # svn server is not available
            print(stderr)


def populate_svn_branch_details(**kwargs):
    # svn_server = os.getenv("SVN_SERVER_NAME", 'svn://************')
    # branch_directory = os.getenv('BRANCH_DIR', '/Applications/Lenox/Branch/')
    svn_server = kwargs['params']['svn_server']
    branch_directory = kwargs['params']['branch_directory']
    project = kwargs['params']['project']

    home_path = os.getenv('HOME', 'E:/KeyPass')
    ref = pkp(
        filename=os.path.join(home_path, 'Database.kdbx'),
        keyfile=os.path.join(home_path, 'Database.key')
    )

    entry = ref.find_entries(title='SVN', first=True)
    user = entry.username
    pwd = entry.password

    command = f'svn ls {svn_server}{branch_directory} --depth immediates --xml --username {user} --password {pwd}'
    p = Popen(command, stdout=PIPE, stderr=PIPE, shell=True)
    stdout, stderr = p.communicate()

    if p.returncode == 0:
        svn_branch_dict = transform_nested_list(xmltodict.parse(stdout.decode())['lists']['list']['entry'])
        df = pd.DataFrame.from_dict(svn_branch_dict).set_index('name').apply(pd.Series.explode).reset_index()
        df.columns = df.columns.str.replace('@', '')
        # Coulmn names: name kind revision             author                         date
        df.rename(columns={'date': 'created_on'}, inplace=True)
        df['svn_server'] = svn_server
        df['branch_directory'] = branch_directory
        df['author_email'] = df['author'] + '@corecard.com'
        session = start_session(project)
        upsert(session, SVNBranch, df, 'name', on_conflict_update=True)
        session.commit()
    else:
        # svn server is not available
        print(stderr)


def add_svn_branch_details():
    populate_svn_branch_details(params={'project': 'plat'})


def add_svn_checkin_details():
    populate_svn_checkin(params={'project': 'plat'})


def is_app_index(filename: str) -> int:
    if '/Core/' in filename and '4.Indexes' in filename:
        return 1
    else:
        return 0


def is_rd_index(filename: str) -> int:
    if 'Reporting' in filename and '4.Indexes_Reporting' in filename:
        return 1
    else:
        return 0


def is_primary_schema_change(filename: str) -> int:
    file_base_name = os.path.basename(filename)
    file_extension = os.path.splitext(filename)[1]

    if file_extension == '.sql' and '/Core/' in filename and '1.Tables' in filename:
        return 1
    else:
        return 0


def is_primary_sql_change(filename: str) -> int:
    file_base_name = os.path.basename(filename)
    file_extension = os.path.splitext(filename)[1]

    if file_extension == '.sql' and file_base_name != 'Version.Sql' \
            and '/Core/' in filename and ('3.Updates' in filename or '2.USPs' in filename):
        return 1
    else:
        return 0


def is_conversion_change(filename: str) -> int:
    file_base_name = os.path.basename(filename)
    file_extension = os.path.splitext(filename)[1]

    if '/Core/' in filename and '5.Conversion' in filename:
        return 1
    else:
        return 0


def is_control_pramater_change(filename: str) -> int:
    file_base_name = os.path.basename(filename)
    file_extension = os.path.splitext(filename)[1]

    if '/Core/' in filename and '6.ControlScripts' in filename:
        return 1
    else:
        return 0


def populate_package_checkin(**kwargs):
    project = kwargs['params']['project']
    svn_server = kwargs['params']['svn_server']

    home_path = os.getenv('HOME', r'E:\KeyPass')
    ref = pkp(
        filename=os.path.join(home_path, 'Database.kdbx'),
        keyfile=os.path.join(home_path, 'Database.key')
    )

    entry = ref.find_entries(title='SVN', first=True)
    user = entry.username
    pwd = entry.password

    for package_dir in kwargs['params']['package_directory']:
        with start_session(project) as session:
            stmt = select(
                SVNPackage.name
            ).select_from(SVNPackage) \
                .filter(SVNPackage.name.like('Plat%')) \
                .filter(SVNPackage.created_on > datetime.now() - timedelta(days=540)) \
                .filter(SVNPackage.name.not_like('%Bad%')) \
                .filter(SVNPackage.package_directory == package_dir)

            result = session.execute(stmt).all()
            for pkg in result:
                command = f'svn log -v --xml "{svn_server}{package_dir}/{pkg[0]}" --username {user} --password {pwd}'
                p = Popen(command, stdout=PIPE, stderr=PIPE, shell=True)
                stdout, stderr = p.communicate()
                if p.returncode == 0:
                    package_dict = xmltodict.parse(stdout.decode())
                    if isinstance(package_dict['log']['logentry'], dict):
                        svn_checkin_dict = transform_nested_list([package_dict['log']['logentry']])
                        df_compare = pd.DataFrame.from_dict(package_dict['log']['logentry'])
                    else:
                        svn_checkin_dict = transform_nested_list(package_dict['log']['logentry'])
                        df_compare = pd.DataFrame.from_dict(package_dict['log']['logentry'])

                    if os.name == "nt":
                        pass
                    else:
                        df_compare.to_csv(f'/tmp/{pkg[0]}_orig_output.csv')

                    df_explode = pd.DataFrame.from_dict(svn_checkin_dict).explode('path')
                    df = pd.concat([df_explode, df_explode['path'].apply(pd.Series)], axis=1)
                    df.drop(columns=['path'], inplace=True)

                    df['cc_jira'] = df['msg'].apply(lambda x: list(set(re.findall(r'PLAT-\d+', x.upper()))))
                    df['client_jira'] = df['msg'].apply(
                        lambda x: list(set(re.findall(r'COOKIE-\d+|CARDS-\d+|CIM-\d+', x.upper()))))
                    df.columns = df.columns.str.replace('@', '')
                    df.columns = df.columns.str.replace('-', '_')
                    df.rename(columns={'date': 'checked_in_on', '#text': 'filename', 'msg': 'comment'}, inplace=True)
                    df['filename'] = df['filename'].str.replace(f'{package_dir}{pkg[0]}', '', regex=True)
                    df['app_indexes'] = df['filename'].str.contains('/Core.*/4.Indexes', 'Y', flags=re.IGNORECASE,
                                                                    na='',
                                                                    regex=True)
                    df['report_indexes'] = df['filename'].str.contains('/Reporting.*/4.Indexes_Reporting', 'Y',
                                                                       flags=re.IGNORECASE, na='',
                                                                       regex=True)
                    df['author_email'] = df['author'] + '@corecard.com'
                    df['svn_server'] = svn_server
                    df['package_name'] = pkg[0]
                    df = df[df['kind'] == 'file']
                    if os.name == 'nt':
                        pass
                    else:
                        df.to_csv(f'/tmp/{pkg[0]}_output.csv')

                    if df.shape[0] > 0:
                        upsert(session, SVNPackageCheckIn, df, 'filename', on_conflict_update=True)
                        df_cc_jira = df[
                            [
                                'svn_server', 'package_name', 'revision',
                                'filename', 'cc_jira', 'checked_in_on', 'author', 'author_email'
                            ]
                        ].explode('cc_jira')
                        df_cc_jira.dropna(subset=['cc_jira'], inplace=True)
                        if os.name == 'nt':
                            pass
                        else:
                            df_cc_jira.to_csv(f'/tmp/{pkg[0]}_cc_jira_output.csv')

                        if df_cc_jira.shape[0] > 0:
                            df_cc_jira['app_index_change'] = df_cc_jira.apply(lambda x: is_app_index(x['filename']),
                                                                              axis=1)
                            df_cc_jira['report_index_change'] = df_cc_jira.apply(lambda x: is_rd_index(x['filename']),
                                                                                 axis=1)
                            df_cc_jira['primary_schema_change'] = df_cc_jira.apply(
                                lambda x: is_primary_schema_change(x['filename']),
                                axis=1)
                            df_cc_jira['primary_sql_change'] = df_cc_jira.apply(
                                lambda x: is_primary_sql_change(x['filename']),
                                axis=1)
                            df_cc_jira['conversion_script'] = df_cc_jira.apply(
                                lambda x: is_conversion_change(x['filename']),
                                axis=1)
                            df_cc_jira['control_parameters'] = df_cc_jira.apply(
                                lambda x: is_control_pramater_change(x['filename']),
                                axis=1)
                            upsert(session, CCJiraPackageCheckIn, df_cc_jira, 'filename', on_conflict_update=True)
                    session.commit()


def populate_svn_checkin(**kwargs):
    svn_server = os.getenv("SVN_SERVER_NAME", 'svn://************')
    branch_directory = os.getenv('BRANCH_DIR', '/Applications/Lenox/Branch/')
    project = kwargs['params']['project']

    home_path = os.getenv('HOME', 'E:/KeyPass')
    ref = pkp(
        filename=os.path.join(home_path, 'Database.kdbx'),
        keyfile=os.path.join(home_path, 'Database.key')
    )

    entry = ref.find_entries(title='SVN', first=True)
    user = entry.username
    pwd = entry.password

    session = start_session(project)
    if sqlalchemy.__version__ == '1.3.24':
        result = session.query(SVNBranch.name).filter(
            SVNBranch.created_on > datetime.today() - timedelta(days=540)).all()
    else:
        stmt = select(
            SVNBranch.name
        ).select_from(SVNBranch).filter(SVNBranch.created_on > datetime.today() - timedelta(days=540))
        result = session.execute(stmt).all()

    for branch_name in result:
        command = f'svn log -v --xml {svn_server}{branch_directory}{branch_name[0]} --username {user} --password {pwd}'
        p = Popen(command, stdout=PIPE, stderr=PIPE, shell=True)
        stdout, stderr = p.communicate()
        if p.returncode == 0:
            svn_dict = xmltodict.parse(stdout.decode())
            if isinstance(svn_dict['log']['logentry'], dict):
                df_compare = pd.DataFrame.from_dict(svn_dict['log']['logentry'])
                svn_checkin_dict = transform_nested_list([svn_dict['log']['logentry']])
            else:
                svn_checkin_dict = transform_nested_list(svn_dict['log']['logentry'])
                df_compare = pd.DataFrame.from_dict(svn_checkin_dict)
            if os.name == 'nt':
                pass
            else:
                df_compare.to_csv(f'/tmp/{branch_name[0]}_orig_output.csv')

            df_explode = pd.DataFrame.from_dict(svn_checkin_dict).explode('path')
            df = pd.concat([df_explode, df_explode['path'].apply(pd.Series)], axis=1)
            df.drop(columns=['path'], inplace=True)

            df['cc_jira'] = df['msg'].apply(lambda x: list(set(re.findall(r'PLAT-\d+', x.upper()))))
            df['client_jira'] = df['msg'].apply(lambda x: list(set(re.findall(r'COOKIE-\d+|CARDS-\d+|CIM-\d+', x.upper()))))
            df.columns = df.columns.str.replace('@', '')
            df.columns = df.columns.str.replace('-', '_')
            df.rename(columns={'date': 'checked_in_on', '#text': 'filename', 'msg': 'comment'}, inplace=True)
            df['filename'] = df['filename'].str.replace(f'{branch_directory}{branch_name[0]}', '', regex=True)
            df['app_indexes'] = df['filename'].str.contains('/Core.*/4.Indexes', 'Y', flags=re.IGNORECASE, na='',
                                                            regex=True)
            df['report_indexes'] = df['filename'].str.contains('/Reporting.*/4.Indexes_Reporting', 'Y',
                                                               flags=re.IGNORECASE, na='',
                                                               regex=True)
            df['svn_server'] = svn_server
            df['branch_name'] = branch_name[0]
            df['author_email'] = df['author'] + '@corecard.com'
            df = df[df['kind'] == 'file']
            if os.name == 'nt':
                pass
            else:
                df.to_csv(f'/tmp/{branch_name[0]}_output.csv')
            if df.shape[0] == 0:
                print(f'No rows: {branch_name}')
            else:
                upsert(session, SVNBranchCheckIn, df, 'filename', on_conflict_update=True)

            if df.shape[0] > 0:
                df_cc_jira = df[
                    [
                        'svn_server', 'branch_name', 'revision',
                        'filename', 'cc_jira', 'checked_in_on', 'author', 'author_email'
                    ]
                ].explode('cc_jira')
                df_cc_jira.dropna(subset=['cc_jira'], inplace=True)
                if os.name == 'nt':
                    pass
                else:
                    df_cc_jira.to_csv(f'/tmp/{branch_name[0]}_cc_jira_output.csv')

                print(df_cc_jira.shape)
                if df_cc_jira.shape[0] > 0:
                    df_cc_jira['app_index_change'] = df_cc_jira.apply(lambda x: is_app_index(x['filename']), axis=1)
                    df_cc_jira['report_index_change'] = df_cc_jira.apply(lambda x: is_rd_index(x['filename']), axis=1)
                    df_cc_jira['primary_schema_change'] = df_cc_jira.apply(
                        lambda x: is_primary_schema_change(x['filename']),
                        axis=1)
                    df_cc_jira['primary_sql_change'] = df_cc_jira.apply(lambda x: is_primary_sql_change(x['filename']),
                                                                        axis=1)
                    df_cc_jira['conversion_script'] = df_cc_jira.apply(lambda x: is_conversion_change(x['filename']),
                                                                       axis=1)
                    df_cc_jira['control_parameters'] = df_cc_jira.apply(
                        lambda x: is_control_pramater_change(x['filename']),
                        axis=1)
                    upsert(session, CCJiraBranchCheckIn, df_cc_jira, 'filename', on_conflict_update=True)
            session.flush()
    session.commit()


def update_platform_version_and_description(**kwargs):
    url = kwargs['params']['url']
    project = kwargs['params']['project']
    page = requests.get(url=url)
    soup = BeautifulSoup(page.content, 'html.parser')
    links = soup.select('a')
    parser = Parser(start_tags=[], end_tags=[], all_data=[], comments=[])
    for link in links:
        parser.feed(link.decode())

    if 'here.' in parser.all_data:
        parser.all_data.remove('here.')
    final_list = [ver.split('released') for ver in parser.all_data]
    df = pd.DataFrame(final_list, columns=['platform_version', 'created_on'])
    df['platform_version'] = df['platform_version'].replace('Platform version ', '', regex=True)
    df['platform_version'] = df['platform_version'].replace(' ', '', regex=True)
    # *********.1  is duplicate
    df.drop_duplicates(subset='platform_version', keep='first', inplace=True)

    # define a 'real' time zone for each abbreviation:
    df['created_on'].fillna("Thu Jan 1 00:00:01 EST 1970", inplace=True)

    tzmapping = {'EDT': tz.gettz('America/Cancun'),
                 'EST': tz.gettz('America/Cancun')}

    df['created_on'] = df['created_on'].apply(date_util_parser.parse, tzinfos=tzmapping)
    df['created_on'] = pd.to_datetime(df['created_on'], exact=True)

    # Get only latest records
    df['created_on'] = pd.to_datetime(df['created_on'], utc=False)
    df = df[df['created_on'] > datetime(2023, 1, 1, tzinfo=pytz.timezone('UTC'))]

    platform_desc_list = []
    for version, created_on in df.to_numpy():
        url = f"{kwargs['params']['url']}/{version.strip()}.html"
        page = requests.get(url=url)
        soup = BeautifulSoup(page.content, 'html.parser')
        links = soup.select('pre')
        line = str(links[0])
        line = re.sub(r'--+', '<hr/>', line)

        parser = Parser(start_tags=[], end_tags=[], all_data=[], comments=[])
        parser.feed(line)
        # for ******** there is no <hr> used
        if len(parser.all_data) == 1:
            parser.feed(parser.all_data[0])

        try:
            parser.all_data.remove('\n')
        except ValueError:
            print(version)
        x = filter(None, [re.sub(r"\d line[s]*", r"", i) for i in parser.all_data])
        x = filter(None, [re.sub(r"Merged revision.*", r"", i) for i in x])

        word_list = []
        for line in (list(x)):
            word_list.append(line.split('|')[-1].translate({ord('\n'): None}))

        platform_desc_list.append([version, word_list, created_on])
    df = pd.DataFrame(platform_desc_list, columns=['platform_version', 'description', 'created_on'])
    if df.shape[0] > 0:
        with start_session(project) as session:
            upsert(session, PlatformVersion, df, 'platform_version', on_conflict_update=True)
            session.commit()


if os.name != 'nt':
    default_args = {
        "owner": "airflow",
        'depends_on_past': False,
        "start_date": datetime(2023, 1, 1),
        "retries": 0,
        "retry_delay": timedelta(minutes=5),
        'schedule': '30 0-23/4 * * 1-5',
    }

    with DAG(
            dag_id='svn-dag-new',
            default_args=default_args,
            tags=['SVN'],
            catchup=False
    ) as dag:
        add_branch_details = PythonOperator(
            task_id='add_branch_details',
            python_callable=populate_svn_branch_details,
            params=
            {
                'project': 'plat',
                'svn_server': 'svn://************',
                'branch_directory': '/Applications/Lenox/Branch/'
            }
        )
        add_package_details = PythonOperator(
            task_id='add_package_details',
            python_callable=populate_svn_package_details,
            params=
            {
                'project': 'plat',
                'svn_server': 'svn://************',
                'package_directory': ['/Applications/PlatProcessingReleasePackage']
            }
        )
        update_checkin_details = PythonOperator(
            task_id="update_checkin_details",
            python_callable=add_svn_checkin_details,
            params={
                'project': 'plat'
            }
        )

        update_platform_version = PythonOperator(
            task_id="update_platform_version",
            python_callable=update_platform_version_and_description,
            params={
                'url': "http://*************",
                'project': 'plat'
            }
        )

        add_package_checkin = PythonOperator(
            task_id="add_package_checkin",
            python_callable=populate_package_checkin,
            params={
                'project': 'plat',
                'svn_server': 'svn://************',
                'package_directory': ['/Applications/PlatProcessingReleasePackage']
            }
        )

        add_branch_details >> update_checkin_details
        add_branch_details >> update_platform_version
        add_branch_details >> add_package_details
        add_branch_details >> add_package_checkin

if __name__ == '__main__':
    populate_svn_branch_details(params=
    {
        'project': 'plat',
        'svn_server': 'svn://************',
        'branch_directory': '/Applications/Lenox/Branch/'
    }
    )

    populate_svn_checkin(params={'project': 'plat', })

    populate_svn_package_details(params=
    {
        'project': 'plat',
        'svn_server': 'svn://************',
        'package_directory': ['/Applications/PlatProcessingReleasePackage']
    }
    )

    populate_package_checkin(params=
    {
        'project': 'plat',
        'svn_server': 'svn://************',
        'package_directory': ['/Applications/PlatProcessingReleasePackage']
    }
    )

    update_platform_version_and_description(params={'url': "http://*************", 'project': 'plat'})
