# Database Configuration
PG_SERVER_NAME=localhost
PG_SERVER_PORT=5432
PG_DB_NAME=jira
PG_SCHEMA=plat

# SVN Configuration
SVN_SERVER_NAME=svn://************
BRANCH_DIR=/Applications/Lenox/Branch/
PACKAGE_DIR=/Applications/PlatProcessingReleasePackage

# KeePass Configuration - Environment specific paths
KEEPASS_DB_PATH=E:/KeyPass/Database.kdbx
KEEPASS_KEY_PATH=E:/KeyPass/Database.key

# KeePass Entry Names
KEEPASS_SVN_ENTRY=SVN
KEEPASS_PG_ENTRY_WINDOWS=PG_DB
KEEPASS_PG_ENTRY_LINUX=jiradb
KEEPASS_JIRA_ENTRY=corecard Jira

# Platform Version URL
PLATFORM_VERSION_URL=http://*************

# Airflow Configuration
AIRFLOW_HOME=/opt/airflow
AIRFLOW__CORE__DAGS_FOLDER=/opt/airflow/dags

# HTTP Headers
HTTP_ACCEPT=application/json
HTTP_CONTENT_TYPE=application/json

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Development Configuration
ENVIRONMENT=development
DEBUG=False

# Connection Pool Settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=2
DB_POOL_RECYCLE=300
DB_CONNECT_TIMEOUT=30
DB_KEEPALIVES=1
DB_KEEPALIVES_IDLE=30
DB_KEEPALIVES_INTERVAL=10
DB_KEEPALIVES_COUNT=5

# Data Processing Configuration
DAYS_LOOKBACK=540
EMAIL_DOMAIN=@corecard.com

# Temporary file paths (Linux/Docker)
TEMP_DIR=/tmp

# SVN Credentials (will be retrieved from KeePass)
# SVN_USERNAME=  # Retrieved from KeePass
# SVN_PASSWORD=  # Retrieved from KeePass

# Database Credentials (will be retrieved from KeePass)
# DB_USERNAME=  # Retrieved from KeePass  
# DB_PASSWORD=  # Retrieved from KeePass
